import { useState } from 'react';
import useLocationStore from '../store/locationStore';

export default function LocationModal() {
  const {
    isLocationModalOpen,
    closeLocationModal,
    updateLocationByZipCode,
    availableLocations,
    setCurrentLocation,
    searchLocations,
    currentLocation
  } = useLocationStore();

  const [zipCode, setZipCode] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('zipcode');

  const handleZipCodeSubmit = async (e) => {
    e.preventDefault();
    if (!zipCode.trim()) return;

    setIsLoading(true);
    setError('');

    try {
      await updateLocationByZipCode(zipCode.trim());
      closeLocationModal();
      setZipCode('');
    } catch (err) {
      setError('Please enter a valid ZIP code');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLocationSelect = (location) => {
    setCurrentLocation(location);
    closeLocationModal();
  };

  const searchResults = searchQuery ? searchLocations(searchQuery) : availableLocations;

  if (!isLocationModalOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Choose your location</h2>
          <button
            onClick={closeLocationModal}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <p className="text-gray-600 mb-6">
            Delivery options and delivery speeds may vary for different locations
          </p>

          {/* Tabs */}
          <div className="flex border-b border-gray-200 mb-6">
            <button
              onClick={() => setActiveTab('zipcode')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'zipcode'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Enter ZIP code
            </button>
            <button
              onClick={() => setActiveTab('browse')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'browse'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Browse locations
            </button>
          </div>

          {/* ZIP Code Tab */}
          {activeTab === 'zipcode' && (
            <div>
              <form onSubmit={handleZipCodeSubmit} className="space-y-4">
                <div>
                  <label htmlFor="zipcode" className="block text-sm font-medium text-gray-700 mb-2">
                    ZIP Code
                  </label>
                  <input
                    type="text"
                    id="zipcode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    placeholder="Enter ZIP code (e.g., 10001)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    maxLength="5"
                  />
                  {error && (
                    <p className="text-red-600 text-sm mt-1">{error}</p>
                  )}
                </div>
                
                <button
                  type="submit"
                  disabled={isLoading || !zipCode.trim()}
                  className="w-full amazon-btn-primary py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Updating...' : 'Apply'}
                </button>
              </form>

              {/* Current Location */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Current location:</h3>
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm text-gray-700">{currentLocation.displayName}</span>
                </div>
              </div>
            </div>
          )}

          {/* Browse Locations Tab */}
          {activeTab === 'browse' && (
            <div>
              {/* Search */}
              <div className="mb-4">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search cities or ZIP codes..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>

              {/* Location List */}
              <div className="max-h-64 overflow-y-auto">
                {searchResults.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No locations found</p>
                ) : (
                  <div className="space-y-2">
                    {searchResults.map((location) => (
                      <button
                        key={location.zipCode}
                        onClick={() => handleLocationSelect(location)}
                        className={`w-full text-left p-3 rounded-lg border transition-colors ${
                          currentLocation.zipCode === location.zipCode
                            ? 'border-orange-500 bg-orange-50'
                            : 'border-gray-200 hover:bg-gray-50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">
                              {location.city}, {location.state}
                            </div>
                            <div className="text-sm text-gray-500">
                              {location.zipCode}
                            </div>
                          </div>
                          {currentLocation.zipCode === location.zipCode && (
                            <svg className="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex items-start space-x-2 text-sm text-gray-600">
            <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="font-medium">Why do we ask for your location?</p>
              <p className="mt-1">
                We use your location to show you delivery options, estimated delivery times, and local availability for products and services.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
