import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import ProductCard from '../components/Amazon/ProductCard';
import { mockProducts } from '../data/products';

export default function CategoryPage() {
  const { category } = useParams();
  const [products, setProducts] = useState([]);
  const [sortBy, setSortBy] = useState('relevance');
  const [filterBy, setFilterBy] = useState({
    priceRange: 'all',
    rating: 'all',
    prime: false,
    freeShipping: false
  });
  const [isLoading, setIsLoading] = useState(true);

  // Format category name for display
  const formatCategoryName = (cat) => {
    return cat.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const categoryName = formatCategoryName(category);

  useEffect(() => {
    setIsLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      let results = mockProducts.filter(product => {
        const productCategory = product.category.toLowerCase().replace(/\s+/g, '-').replace('&', '');
        const urlCategory = category.toLowerCase();
        return productCategory.includes(urlCategory) || urlCategory.includes(productCategory);
      });

      // Apply filters
      if (filterBy.priceRange !== 'all') {
        const [min, max] = filterBy.priceRange.split('-').map(Number);
        results = results.filter(product => {
          if (max) {
            return product.price >= min && product.price <= max;
          } else {
            return product.price >= min;
          }
        });
      }

      if (filterBy.rating !== 'all') {
        const minRating = Number(filterBy.rating);
        results = results.filter(product => product.rating >= minRating);
      }

      if (filterBy.prime) {
        results = results.filter(product => product.prime);
      }

      if (filterBy.freeShipping) {
        results = results.filter(product => product.freeShipping);
      }

      // Sort results
      switch (sortBy) {
        case 'price-low':
          results.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          results.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          results.sort((a, b) => b.rating - a.rating);
          break;
        case 'newest':
          results.sort((a, b) => b.id - a.id);
          break;
        default:
          // Keep original order for relevance
          break;
      }

      setProducts(results);
      setIsLoading(false);
    }, 500);
  }, [category, sortBy, filterBy]);

  const handleFilterChange = (filterType, value) => {
    setFilterBy(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const priceRanges = [
    { label: 'All Prices', value: 'all' },
    { label: 'Under $25', value: '0-25' },
    { label: '$25 to $50', value: '25-50' },
    { label: '$50 to $100', value: '50-100' },
    { label: '$100 to $200', value: '100-200' },
    { label: '$200 & Above', value: '200' }
  ];

  // Category-specific banners and descriptions
  const getCategoryInfo = (cat) => {
    const categoryData = {
      electronics: {
        banner: 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=1200&h=300&fit=crop',
        description: 'Discover the latest in electronics, from smartphones to smart home devices.',
        subcategories: ['Smartphones', 'Laptops', 'Headphones', 'Smart Home', 'Gaming']
      },
      books: {
        banner: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=1200&h=300&fit=crop',
        description: 'Explore millions of books across all genres and formats.',
        subcategories: ['Fiction', 'Non-Fiction', 'Textbooks', 'Children\'s Books', 'E-books']
      },
      clothing: {
        banner: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=300&fit=crop',
        description: 'Fashion for every style and occasion.',
        subcategories: ['Men\'s Clothing', 'Women\'s Clothing', 'Shoes', 'Accessories', 'Jewelry']
      },
      'home-garden': {
        banner: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=1200&h=300&fit=crop',
        description: 'Everything you need to make your house a home.',
        subcategories: ['Furniture', 'Kitchen', 'Bedding', 'Garden', 'Home Decor']
      },
      sports: {
        banner: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1200&h=300&fit=crop',
        description: 'Gear up for your favorite sports and outdoor activities.',
        subcategories: ['Fitness', 'Outdoor Recreation', 'Team Sports', 'Water Sports', 'Winter Sports']
      },
      toys: {
        banner: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1200&h=300&fit=crop',
        description: 'Fun and educational toys for kids of all ages.',
        subcategories: ['Action Figures', 'Building Toys', 'Educational', 'Games', 'Outdoor Toys']
      }
    };

    return categoryData[cat.toLowerCase()] || {
      banner: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=300&fit=crop',
      description: `Discover great products in ${categoryName}.`,
      subcategories: []
    };
  };

  const categoryInfo = getCategoryInfo(category);

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      {/* Category Banner */}
      <div className="relative h-48 bg-gradient-to-r from-blue-600 to-purple-600 overflow-hidden">
        <img 
          src={categoryInfo.banner} 
          alt={categoryName}
          className="w-full h-full object-cover opacity-70"
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-4xl font-bold mb-2">{categoryName}</h1>
            <p className="text-lg">{categoryInfo.description}</p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-2">
            <li>
              <Link to="/" className="text-blue-600 hover:underline">Home</Link>
            </li>
            <li>
              <span className="text-gray-500">/</span>
            </li>
            <li>
              <span className="text-gray-900 font-medium">{categoryName}</span>
            </li>
          </ol>
        </nav>

        {/* Subcategories */}
        {categoryInfo.subcategories.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">Shop by Category</h2>
            <div className="flex flex-wrap gap-2">
              {categoryInfo.subcategories.map((subcat) => (
                <Link
                  key={subcat}
                  to={`/search?q=${encodeURIComponent(subcat)}&category=${category}`}
                  className="bg-white border border-gray-300 rounded-full px-4 py-2 text-sm hover:bg-gray-50 transition-colors"
                >
                  {subcat}
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Results Header and Sort */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900">
              {categoryName} Products
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {isLoading ? 'Loading...' : `${products.length} results`}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <label htmlFor="sort" className="text-sm font-medium text-gray-700">
              Sort by:
            </label>
            <select
              id="sort"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="relevance">Relevance</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Customer Rating</option>
              <option value="newest">Newest Arrivals</option>
            </select>
          </div>
        </div>

        <div className="flex gap-6">
          {/* Filters Sidebar */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="font-bold text-gray-900 mb-4">Filters</h3>
              
              {/* Price Filter */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Price</h4>
                <div className="space-y-2">
                  {priceRanges.map((range) => (
                    <label key={range.value} className="flex items-center">
                      <input
                        type="radio"
                        name="price"
                        value={range.value}
                        checked={filterBy.priceRange === range.value}
                        onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">{range.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Rating Filter */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Customer Rating</h4>
                <div className="space-y-2">
                  {[4, 3, 2, 1].map((rating) => (
                    <label key={rating} className="flex items-center">
                      <input
                        type="radio"
                        name="rating"
                        value={rating}
                        checked={filterBy.rating === rating.toString()}
                        onChange={(e) => handleFilterChange('rating', e.target.value)}
                        className="mr-2"
                      />
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-4 h-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} fill-current`}
                            viewBox="0 0 20 20"
                          >
                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                          </svg>
                        ))}
                        <span className="ml-1 text-sm text-gray-700">& Up</span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Additional Filters */}
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filterBy.prime}
                    onChange={(e) => handleFilterChange('prime', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Prime Eligible</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filterBy.freeShipping}
                    onChange={(e) => handleFilterChange('freeShipping', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Free Shipping</span>
                </label>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="flex-1">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="loading-spinner w-8 h-8"></div>
              </div>
            ) : products.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your filters or browse other categories.
                </p>
                <Link to="/" className="amazon-btn-primary">
                  Continue Shopping
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {products.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
