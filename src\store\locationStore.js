import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useLocationStore = create(
  persist(
    (set, get) => ({
      // State
      currentLocation: {
        zipCode: '10001',
        city: 'New York',
        state: 'NY',
        country: 'USA',
        displayName: 'New York 10001'
      },
      isLocationModalOpen: false,
      availableLocations: [
        {
          zipCode: '10001',
          city: 'New York',
          state: 'NY',
          country: 'USA',
          displayName: 'New York 10001'
        },
        {
          zipCode: '90210',
          city: 'Beverly Hills',
          state: 'CA',
          country: 'USA',
          displayName: 'Beverly Hills 90210'
        },
        {
          zipCode: '60601',
          city: 'Chicago',
          state: 'IL',
          country: 'USA',
          displayName: 'Chicago 60601'
        },
        {
          zipCode: '33101',
          city: 'Miami',
          state: 'FL',
          country: 'USA',
          displayName: 'Miami 33101'
        },
        {
          zipCode: '98101',
          city: 'Seattle',
          state: 'WA',
          country: 'USA',
          displayName: 'Seattle 98101'
        }
      ],
      deliveryOptions: {
        standard: {
          name: 'Standard Delivery',
          price: 0,
          estimatedDays: '5-7',
          description: 'Free standard delivery'
        },
        expedited: {
          name: 'Expedited Delivery',
          price: 9.99,
          estimatedDays: '2-3',
          description: 'Faster delivery for a small fee'
        },
        overnight: {
          name: 'Overnight Delivery',
          price: 19.99,
          estimatedDays: '1',
          description: 'Next business day delivery'
        },
        sameDay: {
          name: 'Same Day Delivery',
          price: 12.99,
          estimatedDays: '0',
          description: 'Delivered today (available in select areas)'
        }
      },

      // Actions
      setCurrentLocation: (location) => {
        set({ currentLocation: location });
      },

      updateLocationByZipCode: async (zipCode) => {
        // Simulate API call to get location details
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Mock location lookup
        const mockLocations = {
          '10001': { city: 'New York', state: 'NY', country: 'USA' },
          '90210': { city: 'Beverly Hills', state: 'CA', country: 'USA' },
          '60601': { city: 'Chicago', state: 'IL', country: 'USA' },
          '33101': { city: 'Miami', state: 'FL', country: 'USA' },
          '98101': { city: 'Seattle', state: 'WA', country: 'USA' },
          '02101': { city: 'Boston', state: 'MA', country: 'USA' },
          '30301': { city: 'Atlanta', state: 'GA', country: 'USA' },
          '75201': { city: 'Dallas', state: 'TX', country: 'USA' },
          '80201': { city: 'Denver', state: 'CO', country: 'USA' },
          '97201': { city: 'Portland', state: 'OR', country: 'USA' }
        };

        const locationData = mockLocations[zipCode];
        if (locationData) {
          const newLocation = {
            zipCode,
            ...locationData,
            displayName: `${locationData.city} ${zipCode}`
          };
          set({ currentLocation: newLocation });
          return newLocation;
        } else {
          throw new Error('Invalid ZIP code');
        }
      },

      openLocationModal: () => set({ isLocationModalOpen: true }),
      closeLocationModal: () => set({ isLocationModalOpen: false }),

      // Get available delivery options for current location
      getDeliveryOptions: (isPrime = false) => {
        const options = get().deliveryOptions;
        const currentLocation = get().currentLocation;
        
        // Modify options based on location and Prime status
        const availableOptions = { ...options };
        
        if (isPrime) {
          availableOptions.standard.price = 0;
          availableOptions.standard.estimatedDays = '2';
          availableOptions.standard.description = 'FREE Two-Day Shipping with Prime';
        }
        
        // Same day delivery only available in major cities
        const sameDayCities = ['New York', 'Los Angeles', 'Chicago', 'Miami', 'Seattle'];
        if (!sameDayCities.includes(currentLocation.city)) {
          delete availableOptions.sameDay;
        }
        
        return availableOptions;
      },

      // Calculate delivery date
      calculateDeliveryDate: (deliveryOption, orderDate = new Date()) => {
        const options = get().deliveryOptions;
        const option = options[deliveryOption];
        
        if (!option) return null;
        
        const deliveryDate = new Date(orderDate);
        const days = parseInt(option.estimatedDays.split('-')[0]) || 0;
        
        if (days === 0) {
          // Same day delivery
          deliveryDate.setHours(23, 59, 59, 999);
        } else {
          deliveryDate.setDate(deliveryDate.getDate() + days);
        }
        
        return deliveryDate;
      },

      // Check if location supports specific features
      supportsFeature: (feature) => {
        const currentLocation = get().currentLocation;
        const majorCities = ['New York', 'Los Angeles', 'Chicago', 'Miami', 'Seattle', 'Boston'];
        
        switch (feature) {
          case 'sameDay':
            return majorCities.includes(currentLocation.city);
          case 'amazonLocker':
            return majorCities.includes(currentLocation.city);
          case 'wholefoods':
            return majorCities.includes(currentLocation.city);
          default:
            return true;
        }
      },

      // Get nearby Amazon facilities (mock data)
      getNearbyFacilities: () => {
        const currentLocation = get().currentLocation;
        
        // Mock facility data based on location
        const facilities = {
          'New York': [
            { type: 'Amazon Locker', name: 'Whole Foods Market', address: '226 E 57th St', distance: '0.5 miles' },
            { type: 'Amazon Hub', name: 'CVS Pharmacy', address: '150 E 42nd St', distance: '0.8 miles' },
            { type: 'Whole Foods', name: 'Whole Foods Market', address: '4 Union Square S', distance: '1.2 miles' }
          ],
          'Beverly Hills': [
            { type: 'Amazon Locker', name: 'Whole Foods Market', address: '239 N Canon Dr', distance: '0.3 miles' },
            { type: 'Amazon Hub', name: 'CVS Pharmacy', address: '9570 W Pico Blvd', distance: '1.1 miles' }
          ],
          'Chicago': [
            { type: 'Amazon Locker', name: 'Whole Foods Market', address: '30 W Huron St', distance: '0.4 miles' },
            { type: 'Amazon Hub', name: 'Walgreens', address: '757 N Michigan Ave', distance: '0.7 miles' }
          ]
        };
        
        return facilities[currentLocation.city] || [];
      },

      // Search locations
      searchLocations: (query) => {
        const locations = get().availableLocations;
        const searchTerm = query.toLowerCase();
        
        return locations.filter(location =>
          location.city.toLowerCase().includes(searchTerm) ||
          location.state.toLowerCase().includes(searchTerm) ||
          location.zipCode.includes(searchTerm)
        );
      }
    }),
    {
      name: 'location-storage',
      partialize: (state) => ({
        currentLocation: state.currentLocation
      })
    }
  )
);

export default useLocationStore;
