import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useWishlistStore = create(
  persist(
    (set, get) => ({
      // State
      items: [],
      lists: [
        {
          id: 'default',
          name: 'My Wish List',
          description: 'Items I want to buy',
          isDefault: true,
          createdAt: new Date().toISOString(),
          items: []
        }
      ],

      // Computed values
      get totalItems() {
        return get().items.length;
      },

      get getItemsByList() {
        return (listId) => {
          const list = get().lists.find(l => l.id === listId);
          return list ? list.items : [];
        };
      },

      get isInWishlist() {
        return (productId, listId = 'default') => {
          const list = get().lists.find(l => l.id === listId);
          return list ? list.items.some(item => item.id === productId) : false;
        };
      },

      // Actions
      addToWishlist: (product, listId = 'default') => {
        const lists = get().lists;
        const listIndex = lists.findIndex(l => l.id === listId);
        
        if (listIndex === -1) return; // List doesn't exist
        
        const list = lists[listIndex];
        const existingItem = list.items.find(item => item.id === product.id);
        
        if (!existingItem) {
          const updatedLists = [...lists];
          updatedLists[listIndex] = {
            ...list,
            items: [...list.items, {
              ...product,
              addedAt: new Date().toISOString()
            }]
          };
          
          set({ 
            lists: updatedLists,
            items: get().getAllItems()
          });
        }
      },

      removeFromWishlist: (productId, listId = 'default') => {
        const lists = get().lists;
        const listIndex = lists.findIndex(l => l.id === listId);
        
        if (listIndex === -1) return;
        
        const list = lists[listIndex];
        const updatedLists = [...lists];
        updatedLists[listIndex] = {
          ...list,
          items: list.items.filter(item => item.id !== productId)
        };
        
        set({ 
          lists: updatedLists,
          items: get().getAllItems()
        });
      },

      toggleWishlist: (product, listId = 'default') => {
        const isInList = get().isInWishlist(product.id, listId);
        if (isInList) {
          get().removeFromWishlist(product.id, listId);
        } else {
          get().addToWishlist(product, listId);
        }
      },

      createList: (name, description = '') => {
        const newList = {
          id: Date.now().toString(),
          name,
          description,
          isDefault: false,
          createdAt: new Date().toISOString(),
          items: []
        };
        
        set({
          lists: [...get().lists, newList]
        });
        
        return newList.id;
      },

      updateList: (listId, updates) => {
        const lists = get().lists;
        const listIndex = lists.findIndex(l => l.id === listId);
        
        if (listIndex === -1) return;
        
        const updatedLists = [...lists];
        updatedLists[listIndex] = {
          ...updatedLists[listIndex],
          ...updates
        };
        
        set({ lists: updatedLists });
      },

      deleteList: (listId) => {
        const lists = get().lists;
        const list = lists.find(l => l.id === listId);
        
        // Don't delete default list
        if (!list || list.isDefault) return;
        
        set({
          lists: lists.filter(l => l.id !== listId),
          items: get().getAllItems()
        });
      },

      clearList: (listId) => {
        const lists = get().lists;
        const listIndex = lists.findIndex(l => l.id === listId);
        
        if (listIndex === -1) return;
        
        const updatedLists = [...lists];
        updatedLists[listIndex] = {
          ...updatedLists[listIndex],
          items: []
        };
        
        set({ 
          lists: updatedLists,
          items: get().getAllItems()
        });
      },

      moveToList: (productId, fromListId, toListId) => {
        const lists = get().lists;
        const fromList = lists.find(l => l.id === fromListId);
        const toList = lists.find(l => l.id === toListId);
        
        if (!fromList || !toList) return;
        
        const item = fromList.items.find(item => item.id === productId);
        if (!item) return;
        
        // Remove from source list
        get().removeFromWishlist(productId, fromListId);
        
        // Add to destination list
        get().addToWishlist(item, toListId);
      },

      // Helper function to get all items across all lists
      getAllItems: () => {
        const lists = get().lists;
        const allItems = [];
        const seenIds = new Set();
        
        lists.forEach(list => {
          list.items.forEach(item => {
            if (!seenIds.has(item.id)) {
              allItems.push(item);
              seenIds.add(item.id);
            }
          });
        });
        
        return allItems;
      },

      // Get list by ID
      getListById: (listId) => {
        return get().lists.find(l => l.id === listId);
      },

      // Get default list
      getDefaultList: () => {
        return get().lists.find(l => l.isDefault) || get().lists[0];
      }
    }),
    {
      name: 'wishlist-storage',
      partialize: (state) => ({
        lists: state.lists
      })
    }
  )
);

export default useWishlistStore;
