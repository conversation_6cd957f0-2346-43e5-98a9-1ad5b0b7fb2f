import { useState } from 'react';
import ProductCard from './ProductCard';

export default function ProductGrid({ products, title, showMore = false, initialDisplayCount = 8 }) {
  const [displayCount, setDisplayCount] = useState(initialDisplayCount);

  const displayedProducts = products.slice(0, displayCount);
  const hasMoreProducts = products.length > displayCount;
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      {/* Section Title */}
      {title && (
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">{title}</h2>
          {showMore && hasMoreProducts && (
            <button
              onClick={() => setDisplayCount(prev => prev + initialDisplayCount)}
              className="amazon-link text-sm hover:underline"
            >
              See more
            </button>
          )}
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {displayedProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {/* Show More Button at Bottom */}
      {showMore && hasMoreProducts && (
        <div className="text-center mt-6">
          <button
            onClick={() => setDisplayCount(prev => prev + initialDisplayCount)}
            className="amazon-btn-secondary px-6 py-2"
          >
            Show More Products
          </button>
        </div>
      )}
    </div>
  );
}
