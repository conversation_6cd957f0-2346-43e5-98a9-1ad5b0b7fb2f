import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import useOrderStore from '../store/orderStore';

export default function OrderConfirmation() {
  const { orderId } = useParams();
  const { getOrderById } = useOrderStore();
  const [order, setOrder] = useState(null);

  useEffect(() => {
    if (orderId) {
      const foundOrder = getOrderById(orderId);
      setOrder(foundOrder);
    }
  }, [orderId, getOrderById]);

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-100">
        <AmazonHeader />
        <AmazonNavigation />
        <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Order Not Found</h1>
          <p className="text-gray-600 mb-6">We couldn't find the order you're looking for.</p>
          <Link to="/orders" className="amazon-btn-primary">
            View All Orders
          </Link>
        </div>
        <AmazonFooter />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      <div className="max-w-4xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">✅</div>
          <h1 className="text-3xl font-bold text-green-600 mb-2">Order Placed Successfully!</h1>
          <p className="text-lg text-gray-600">
            Thank you for your order. We'll send you a confirmation email shortly.
          </p>
        </div>

        {/* Order Details */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
          <div className="bg-green-50 px-6 py-4 border-b border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-green-800">Order #{order.id}</h2>
                <p className="text-sm text-green-600">
                  Placed on {new Date(order.date).toLocaleDateString()}
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-green-800">${order.total.toFixed(2)}</div>
                <div className="text-sm text-green-600">Total</div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {/* Order Items */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Items Ordered</h3>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-16 h-16 object-contain rounded border"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 line-clamp-2">{item.title}</h4>
                      <div className="text-sm text-gray-600 mt-1">
                        Quantity: {item.quantity} × ${item.price}
                      </div>
                      {item.prime && (
                        <div className="text-xs text-blue-600 mt-1">
                          ⭐ Prime eligible
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-900">
                        ${(item.price * item.quantity).toFixed(2)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Shipping Address */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Shipping Address</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-900">
                    <div className="font-medium">{order.shippingAddress.name}</div>
                    <div>{order.shippingAddress.street}</div>
                    <div>
                      {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                    </div>
                    <div>{order.shippingAddress.country}</div>
                  </div>
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Payment Method</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm text-gray-900">
                    <div className="font-medium">{order.paymentMethod.type}</div>
                    <div>**** **** **** {order.paymentMethod.last4}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Totals */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="max-w-md ml-auto space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>${order.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Tax:</span>
                  <span>${order.tax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping:</span>
                  <span>{order.shipping === 0 ? 'FREE' : `$${order.shipping.toFixed(2)}`}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold border-t border-gray-200 pt-2">
                  <span>Total:</span>
                  <span>${order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delivery Information */}
        <div className="bg-blue-50 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-3">
            <div className="text-2xl">🚚</div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Delivery Information</h3>
              <div className="text-blue-800">
                <p className="mb-2">
                  <strong>Estimated Delivery:</strong> {new Date(order.estimatedDelivery).toLocaleDateString()}
                </p>
                <p className="mb-2">
                  <strong>Tracking Number:</strong> {order.trackingNumber}
                </p>
                <p className="text-sm">
                  We'll send you tracking information once your order ships. You can also track your order in your account.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">What's Next?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4">
              <div className="text-3xl mb-2">📧</div>
              <h4 className="font-medium text-gray-900 mb-1">Confirmation Email</h4>
              <p className="text-sm text-gray-600">
                Check your email for order confirmation and receipt
              </p>
            </div>
            
            <div className="text-center p-4">
              <div className="text-3xl mb-2">📦</div>
              <h4 className="font-medium text-gray-900 mb-1">Order Processing</h4>
              <p className="text-sm text-gray-600">
                We'll prepare your items and send shipping notification
              </p>
            </div>
            
            <div className="text-3xl mb-2">🚚</div>
              <h4 className="font-medium text-gray-900 mb-1">Track Delivery</h4>
              <p className="text-sm text-gray-600">
                Follow your package's journey to your doorstep
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/orders"
            className="amazon-btn-primary text-center px-6 py-3"
          >
            View All Orders
          </Link>
          
          <Link
            to="/"
            className="amazon-btn-secondary text-center px-6 py-3"
          >
            Continue Shopping
          </Link>
          
          <button
            onClick={() => window.print()}
            className="amazon-btn-secondary px-6 py-3"
          >
            Print Receipt
          </button>
        </div>

        {/* Customer Service */}
        <div className="text-center mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
          <p className="text-gray-600 mb-4">
            Our customer service team is here to help with any questions about your order.
          </p>
          <Link
            to="/customer-service"
            className="amazon-link"
          >
            Contact Customer Service
          </Link>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
