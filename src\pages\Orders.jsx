import { useState } from 'react';
import { Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import useAuthStore from '../store/authStore';
import useOrderStore from '../store/orderStore';

export default function Orders() {
  const { user } = useAuthStore();
  const { orders, searchOrders } = useOrderStore();
  const [timeFilter, setTimeFilter] = useState('last30');
  const [searchQuery, setSearchQuery] = useState('');

  // Get filtered orders
  const getFilteredOrders = () => {
    let filteredOrders = orders;

    // Apply search filter
    if (searchQuery.trim()) {
      filteredOrders = searchOrders(searchQuery.trim());
    }

    // Apply time filter
    const now = new Date();
    const filterDate = new Date();

    switch (timeFilter) {
      case 'last30':
        filterDate.setDate(now.getDate() - 30);
        break;
      case 'last3months':
        filterDate.setMonth(now.getMonth() - 3);
        break;
      case 'lastyear':
        filterDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        return filteredOrders;
    }

    return filteredOrders.filter(order => new Date(order.date) >= filterDate);
  };

  const filteredOrders = getFilteredOrders();

  // Mock orders data for demo (remove this in production)
  const mockOrders = orders.length === 0 ? [
    {
      id: 'ORDER-001',
      date: '2024-01-15',
      status: 'Delivered',
      total: 299.99,
      items: [
        {
          id: 1,
          title: 'Sony WH-1000XM5 Wireless Noise Canceling Headphones',
          image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=100&h=100&fit=crop',
          price: 299.99,
          quantity: 1
        }
      ],
      deliveryDate: '2024-01-18',
      trackingNumber: 'TRK123456789'
    },
    {
      id: 'ORDER-002',
      date: '2024-01-10',
      status: 'Shipped',
      total: 89.99,
      items: [
        {
          id: 6,
          title: 'Instant Pot Duo 7-in-1 Electric Pressure Cooker',
          image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=100&h=100&fit=crop',
          price: 89.99,
          quantity: 1
        }
      ],
      estimatedDelivery: '2024-01-20',
      trackingNumber: 'TRK987654321'
    },
    {
      id: 'ORDER-003',
      date: '2024-01-05',
      status: 'Processing',
      total: 1199.99,
      items: [
        {
          id: 1,
          title: 'Apple iPhone 15 Pro Max, 256GB, Natural Titanium',
          image: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=100&h=100&fit=crop',
          price: 1199.99,
          quantity: 1
        }
      ],
      estimatedShip: '2024-01-22'
    }
  ] : [];

  // Use real orders if available, otherwise show mock data for demo
  const displayOrders = filteredOrders.length > 0 ? filteredOrders : mockOrders;

  const getStatusColor = (status) => {
    switch (status) {
      case 'Delivered':
        return 'text-green-600 bg-green-100';
      case 'Shipped':
        return 'text-blue-600 bg-blue-100';
      case 'Processing':
        return 'text-yellow-600 bg-yellow-100';
      case 'Cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };



  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Your Orders</h1>
          <p className="text-gray-600">
            Track packages, review or buy items again, and manage returns
          </p>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4">
              <div>
                <label htmlFor="timeFilter" className="block text-sm font-medium text-gray-700 mb-1">
                  Time Period
                </label>
                <select
                  id="timeFilter"
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  <option value="last30">Last 30 days</option>
                  <option value="last3months">Last 3 months</option>
                  <option value="lastyear">Last year</option>
                  <option value="all">All orders</option>
                </select>
              </div>
            </div>
            
            <div className="flex-1 max-w-md">
              <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
                Search Orders
              </label>
              <input
                type="text"
                id="search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search by order ID or item name"
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-6">
          {displayOrders.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">📦</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery ? 'Try adjusting your search terms.' : 'You haven\'t placed any orders yet.'}
              </p>
              <Link to="/" className="amazon-btn-primary">
                Start Shopping
              </Link>
            </div>
          ) : (
            displayOrders.map((order) => (
              <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                {/* Order Header */}
                <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div className="flex items-center space-x-6 mb-2 md:mb-0">
                      <div>
                        <div className="text-sm text-gray-600">Order placed</div>
                        <div className="font-medium">{new Date(order.date).toLocaleDateString()}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Total</div>
                        <div className="font-medium">${order.total}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Ship to</div>
                        <div className="font-medium">{user?.name}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {order.status}
                      </span>
                      <div className="text-sm text-gray-600">
                        Order # {order.id}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="p-6">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex items-start space-x-4 mb-4 last:mb-0">
                      <img
                        src={item.image}
                        alt={item.title}
                        className="w-20 h-20 object-contain rounded border"
                      />
                      <div className="flex-1 min-w-0">
                        <Link
                          to={`/product/${item.id}`}
                          className="text-blue-600 hover:underline font-medium line-clamp-2"
                        >
                          {item.title}
                        </Link>
                        <div className="text-sm text-gray-600 mt-1">
                          Quantity: {item.quantity} | Price: ${item.price}
                        </div>
                        
                        {/* Delivery Info */}
                        <div className="mt-2 text-sm">
                          {order.status === 'Delivered' && (
                            <div className="text-green-600">
                              ✓ Delivered on {new Date(order.deliveryDate).toLocaleDateString()}
                            </div>
                          )}
                          {order.status === 'Shipped' && (
                            <div className="text-blue-600">
                              🚚 Estimated delivery: {new Date(order.estimatedDelivery).toLocaleDateString()}
                            </div>
                          )}
                          {order.status === 'Processing' && (
                            <div className="text-yellow-600">
                              ⏳ Estimated ship date: {new Date(order.estimatedShip).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="flex flex-col space-y-2">
                        {order.status === 'Delivered' && (
                          <>
                            <button className="amazon-btn-primary text-sm px-4 py-2">
                              Buy it again
                            </button>
                            <button className="amazon-btn-secondary text-sm px-4 py-2">
                              Return item
                            </button>
                            <button className="amazon-btn-secondary text-sm px-4 py-2">
                              Write review
                            </button>
                          </>
                        )}
                        {order.status === 'Shipped' && (
                          <button className="amazon-btn-primary text-sm px-4 py-2">
                            Track package
                          </button>
                        )}
                        {order.status === 'Processing' && (
                          <button className="amazon-btn-secondary text-sm px-4 py-2">
                            Cancel item
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {/* Tracking Info */}
                  {order.trackingNumber && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        Tracking number: <span className="font-mono">{order.trackingNumber}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Order Actions */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Need help with an order?</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/customer-service"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-2xl mr-3">💬</div>
              <div>
                <div className="font-medium text-gray-900">Contact us</div>
                <div className="text-sm text-gray-600">Get help with your order</div>
              </div>
            </Link>
            
            <Link
              to="/customer-service"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-2xl mr-3">↩️</div>
              <div>
                <div className="font-medium text-gray-900">Returns & refunds</div>
                <div className="text-sm text-gray-600">Return or exchange items</div>
              </div>
            </Link>
            
            <Link
              to="/customer-service"
              className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-2xl mr-3">📋</div>
              <div>
                <div className="font-medium text-gray-900">Order issues</div>
                <div className="text-sm text-gray-600">Report problems with orders</div>
              </div>
            </Link>
          </div>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
