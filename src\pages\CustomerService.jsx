import { useState } from 'react';
import { Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';

export default function CustomerService() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');

  const helpTopics = [
    {
      category: 'Orders',
      icon: '📦',
      topics: [
        'Track your package',
        'Cancel items or orders',
        'Return or replace items',
        'Change your order',
        'Order issues'
      ]
    },
    {
      category: 'Delivery',
      icon: '🚚',
      topics: [
        'Delivery options',
        'Missing packages',
        'Delivery instructions',
        'Amazon lockers',
        'Shipping rates'
      ]
    },
    {
      category: 'Returns & Refunds',
      icon: '↩️',
      topics: [
        'Return policy',
        'Print return labels',
        'Refund status',
        'Exchange items',
        'Return to Amazon'
      ]
    },
    {
      category: 'Account & Login',
      icon: '👤',
      topics: [
        'Sign in issues',
        'Account settings',
        'Password reset',
        'Two-step verification',
        'Close your account'
      ]
    },
    {
      category: 'Prime',
      icon: '⭐',
      topics: [
        'Prime benefits',
        'Manage Prime membership',
        'Prime Video',
        'Prime Music',
        'Prime shipping'
      ]
    },
    {
      category: 'Payments',
      icon: '💳',
      topics: [
        'Payment methods',
        'Gift cards',
        'Promotional codes',
        'Billing issues',
        'Amazon Pay'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Track a Package',
      description: 'Get real-time updates on your order',
      icon: '📍',
      action: 'Track Package'
    },
    {
      title: 'Return an Item',
      description: 'Start a return or exchange',
      icon: '📤',
      action: 'Start Return'
    },
    {
      title: 'Cancel an Order',
      description: 'Cancel items before they ship',
      icon: '❌',
      action: 'Cancel Order'
    },
    {
      title: 'Contact Us',
      description: 'Chat, call, or email support',
      icon: '💬',
      action: 'Contact Support'
    }
  ];

  const handleSearch = (e) => {
    e.preventDefault();
    // In a real app, this would search the help database
    console.log('Searching for:', searchQuery);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      {/* Hero Section */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-12 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              How can we help you today?
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              Find answers to your questions or get in touch with our support team
            </p>
            
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="flex">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Describe your issue or question..."
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
                <button
                  type="submit"
                  className="bg-orange-400 hover:bg-orange-500 px-6 py-3 border border-orange-400 rounded-r-lg text-black font-medium transition-colors"
                >
                  Search
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        {/* Quick Actions */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer">
                <div className="text-3xl mb-3">{action.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{action.title}</h3>
                <p className="text-gray-600 text-sm mb-4">{action.description}</p>
                <button className="amazon-btn-primary text-sm">
                  {action.action}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Help Topics */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Browse Help Topics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {helpTopics.map((category, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center mb-4">
                  <span className="text-2xl mr-3">{category.icon}</span>
                  <h3 className="text-lg font-semibold text-gray-900">{category.category}</h3>
                </div>
                <ul className="space-y-2">
                  {category.topics.map((topic, topicIndex) => (
                    <li key={topicIndex}>
                      <button className="text-blue-600 hover:underline text-sm text-left">
                        {topic}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Options */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Still need help? Contact us
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl mb-4">💬</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Chat</h3>
              <p className="text-gray-600 text-sm mb-4">
                Get instant help from our support team
              </p>
              <button className="amazon-btn-primary">Start Chat</button>
              <p className="text-xs text-gray-500 mt-2">Available 24/7</p>
            </div>
            
            <div className="text-center">
              <div className="text-4xl mb-4">📞</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Phone</h3>
              <p className="text-gray-600 text-sm mb-4">
                Speak directly with a customer service representative
              </p>
              <button className="amazon-btn-primary">Request Call</button>
              <p className="text-xs text-gray-500 mt-2">We'll call you back</p>
            </div>
            
            <div className="text-center">
              <div className="text-4xl mb-4">✉️</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Email</h3>
              <p className="text-gray-600 text-sm mb-4">
                Send us a detailed message about your issue
              </p>
              <button className="amazon-btn-primary">Send Email</button>
              <p className="text-xs text-gray-500 mt-2">Response within 24 hours</p>
            </div>
          </div>
        </div>

        {/* Popular Articles */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Popular Help Articles</h2>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 divide-y divide-gray-200">
            {[
              'How to track your package',
              'Return policy and procedures',
              'How to cancel an order',
              'Prime membership benefits',
              'Payment methods and billing',
              'Delivery options and timing',
              'Account security and two-step verification',
              'Gift card terms and conditions'
            ].map((article, index) => (
              <div key={index} className="p-4 hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center justify-between">
                  <span className="text-gray-900">{article}</span>
                  <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Feedback */}
        <div className="mt-12 bg-blue-50 rounded-lg p-8 text-center">
          <h3 className="text-xl font-semibold text-blue-900 mb-4">
            How are we doing?
          </h3>
          <p className="text-blue-800 mb-6">
            Your feedback helps us improve our customer service experience.
          </p>
          <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors">
            Leave Feedback
          </button>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
