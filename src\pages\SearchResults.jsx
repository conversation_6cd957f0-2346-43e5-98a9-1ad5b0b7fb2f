import { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import ProductCard from '../components/Amazon/ProductCard';
import { mockProducts } from '../data/products';

export default function SearchResults() {
  const [searchParams] = useSearchParams();
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [sortBy, setSortBy] = useState('relevance');
  const [filterBy, setFilterBy] = useState({
    category: 'all',
    priceRange: 'all',
    rating: 'all',
    prime: false,
    freeShipping: false
  });
  const [isLoading, setIsLoading] = useState(true);

  const query = searchParams.get('q') || '';
  const category = searchParams.get('category') || 'all';

  useEffect(() => {
    setIsLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      let results = mockProducts;

      // Filter by search query
      if (query) {
        results = results.filter(product =>
          product.title.toLowerCase().includes(query.toLowerCase()) ||
          product.category.toLowerCase().includes(query.toLowerCase())
        );
      }

      // Filter by category from URL
      if (category !== 'all') {
        results = results.filter(product =>
          product.category.toLowerCase().includes(category.toLowerCase())
        );
      }

      // Apply additional filters
      if (filterBy.category !== 'all') {
        results = results.filter(product =>
          product.category.toLowerCase().includes(filterBy.category.toLowerCase())
        );
      }

      if (filterBy.priceRange !== 'all') {
        const [min, max] = filterBy.priceRange.split('-').map(Number);
        results = results.filter(product => {
          if (max) {
            return product.price >= min && product.price <= max;
          } else {
            return product.price >= min;
          }
        });
      }

      if (filterBy.rating !== 'all') {
        const minRating = Number(filterBy.rating);
        results = results.filter(product => product.rating >= minRating);
      }

      if (filterBy.prime) {
        results = results.filter(product => product.prime);
      }

      if (filterBy.freeShipping) {
        results = results.filter(product => product.freeShipping);
      }

      // Sort results
      switch (sortBy) {
        case 'price-low':
          results.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          results.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          results.sort((a, b) => b.rating - a.rating);
          break;
        case 'newest':
          results.sort((a, b) => b.id - a.id);
          break;
        default:
          // Keep original order for relevance
          break;
      }

      setFilteredProducts(results);
      setIsLoading(false);
    }, 500);
  }, [query, category, sortBy, filterBy]);

  const handleFilterChange = (filterType, value) => {
    setFilterBy(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const categories = ['all', 'Electronics', 'Books', 'Clothing & Shoes', 'Home & Kitchen', 'Sports & Outdoors', 'Toys & Games'];
  const priceRanges = [
    { label: 'All Prices', value: 'all' },
    { label: 'Under $25', value: '0-25' },
    { label: '$25 to $50', value: '25-50' },
    { label: '$50 to $100', value: '50-100' },
    { label: '$100 to $200', value: '100-200' },
    { label: '$200 & Above', value: '200' }
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Search Results Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {query ? `Results for "${query}"` : `${category} Products`}
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                {isLoading ? 'Searching...' : `${filteredProducts.length} results`}
              </p>
            </div>
            
            {/* Sort Dropdown */}
            <div className="flex items-center space-x-2">
              <label htmlFor="sort" className="text-sm font-medium text-gray-700">
                Sort by:
              </label>
              <select
                id="sort"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="relevance">Relevance</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Customer Rating</option>
                <option value="newest">Newest Arrivals</option>
              </select>
            </div>
          </div>
        </div>

        <div className="flex gap-6">
          {/* Filters Sidebar */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="font-bold text-gray-900 mb-4">Filters</h3>
              
              {/* Category Filter */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Category</h4>
                <div className="space-y-2">
                  {categories.map((cat) => (
                    <label key={cat} className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        value={cat}
                        checked={filterBy.category === cat}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 capitalize">{cat}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Filter */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Price</h4>
                <div className="space-y-2">
                  {priceRanges.map((range) => (
                    <label key={range.value} className="flex items-center">
                      <input
                        type="radio"
                        name="price"
                        value={range.value}
                        checked={filterBy.priceRange === range.value}
                        onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">{range.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Rating Filter */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-2">Customer Rating</h4>
                <div className="space-y-2">
                  {[4, 3, 2, 1].map((rating) => (
                    <label key={rating} className="flex items-center">
                      <input
                        type="radio"
                        name="rating"
                        value={rating}
                        checked={filterBy.rating === rating.toString()}
                        onChange={(e) => handleFilterChange('rating', e.target.value)}
                        className="mr-2"
                      />
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-4 h-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} fill-current`}
                            viewBox="0 0 20 20"
                          >
                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                          </svg>
                        ))}
                        <span className="ml-1 text-sm text-gray-700">& Up</span>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Additional Filters */}
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filterBy.prime}
                    onChange={(e) => handleFilterChange('prime', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Prime Eligible</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filterBy.freeShipping}
                    onChange={(e) => handleFilterChange('freeShipping', e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Free Shipping</span>
                </label>
              </div>
            </div>
          </div>

          {/* Results Grid */}
          <div className="flex-1">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="loading-spinner w-8 h-8"></div>
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search or filters to find what you're looking for.
                </p>
                <Link to="/" className="amazon-btn-primary">
                  Continue Shopping
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredProducts.map((product) => (
                  <ProductCard key={product.id} product={product} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
