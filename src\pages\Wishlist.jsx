import { useState } from 'react';
import { Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import useWishlistStore from '../store/wishlistStore';
import useCartStore from '../store/cartStore';

export default function Wishlist() {
  const { 
    lists, 
    removeFromWishlist, 
    createList, 
    deleteList, 
    updateList,
    moveToList,
    getListById 
  } = useWishlistStore();
  const { addItem } = useCartStore();
  
  const [activeListId, setActiveListId] = useState('default');
  const [showCreateList, setShowCreateList] = useState(false);
  const [newListName, setNewListName] = useState('');
  const [newListDescription, setNewListDescription] = useState('');
  const [editingList, setEditingList] = useState(null);

  const activeList = getListById(activeListId) || lists[0];
  const items = activeList?.items || [];

  const handleCreateList = (e) => {
    e.preventDefault();
    if (newListName.trim()) {
      const newListId = createList(newListName.trim(), newListDescription.trim());
      setActiveListId(newListId);
      setNewListName('');
      setNewListDescription('');
      setShowCreateList(false);
    }
  };

  const handleUpdateList = (e) => {
    e.preventDefault();
    if (editingList && newListName.trim()) {
      updateList(editingList.id, {
        name: newListName.trim(),
        description: newListDescription.trim()
      });
      setEditingList(null);
      setNewListName('');
      setNewListDescription('');
    }
  };

  const handleDeleteList = (listId) => {
    if (window.confirm('Are you sure you want to delete this list?')) {
      deleteList(listId);
      if (activeListId === listId) {
        setActiveListId('default');
      }
    }
  };

  const handleMoveToCart = (item) => {
    addItem(item);
    removeFromWishlist(item.id, activeListId);
  };

  const startEditingList = (list) => {
    setEditingList(list);
    setNewListName(list.name);
    setNewListDescription(list.description);
    setShowCreateList(true);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Your Lists</h1>
          <p className="text-gray-600">
            Manage your wish lists and save items for later
          </p>
        </div>

        <div className="flex gap-6">
          {/* Lists Sidebar */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-bold text-gray-900">Your Lists</h3>
                <button
                  onClick={() => setShowCreateList(true)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                  title="Create new list"
                >
                  + New
                </button>
              </div>
              
              <div className="space-y-2">
                {lists.map((list) => (
                  <div key={list.id} className="group">
                    <button
                      onClick={() => setActiveListId(list.id)}
                      className={`w-full text-left p-2 rounded transition-colors ${
                        activeListId === list.id
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="font-medium text-sm">{list.name}</div>
                      <div className="text-xs text-gray-500">
                        {list.items.length} items
                      </div>
                    </button>
                    
                    {!list.isDefault && (
                      <div className="flex items-center justify-end space-x-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={() => startEditingList(list)}
                          className="text-xs text-gray-500 hover:text-blue-600"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteList(list.id)}
                          className="text-xs text-gray-500 hover:text-red-600"
                        >
                          Delete
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Create/Edit List Form */}
            {showCreateList && (
              <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                <h4 className="font-medium text-gray-900 mb-3">
                  {editingList ? 'Edit List' : 'Create New List'}
                </h4>
                <form onSubmit={editingList ? handleUpdateList : handleCreateList}>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      List Name
                    </label>
                    <input
                      type="text"
                      value={newListName}
                      onChange={(e) => setNewListName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter list name"
                      required
                    />
                  </div>
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description (optional)
                    </label>
                    <textarea
                      value={newListDescription}
                      onChange={(e) => setNewListDescription(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter description"
                      rows="2"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="submit"
                      className="amazon-btn-primary text-sm px-3 py-1"
                    >
                      {editingList ? 'Update' : 'Create'}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShowCreateList(false);
                        setEditingList(null);
                        setNewListName('');
                        setNewListDescription('');
                      }}
                      className="amazon-btn-secondary text-sm px-3 py-1"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>

          {/* List Content */}
          <div className="flex-1">
            {activeList && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">{activeList.name}</h2>
                    {activeList.description && (
                      <p className="text-gray-600 mt-1">{activeList.description}</p>
                    )}
                    <p className="text-sm text-gray-500 mt-1">
                      {items.length} items • Created {new Date(activeList.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {items.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">💝</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Your list is empty</h3>
                    <p className="text-gray-600 mb-4">
                      Browse products and add them to your list by clicking the heart icon.
                    </p>
                    <Link to="/" className="amazon-btn-primary">
                      Start Shopping
                    </Link>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {items.map((item) => (
                      <div key={item.id} className="border border-gray-200 rounded-lg p-4">
                        <Link to={`/product/${item.id}`} className="block mb-3">
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-48 object-contain mb-3"
                          />
                          <h3 className="font-medium text-gray-900 line-clamp-2 hover:text-blue-600">
                            {item.title}
                          </h3>
                        </Link>
                        
                        <div className="mb-3">
                          <span className="amazon-price text-lg">${item.price}</span>
                          {item.originalPrice && item.originalPrice > item.price && (
                            <span className="ml-2 text-sm text-gray-500 line-through">
                              ${item.originalPrice}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center space-x-2 mb-3">
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className={`w-4 h-4 ${i < Math.floor(item.rating) ? 'text-yellow-400' : 'text-gray-300'} fill-current`}
                              viewBox="0 0 20 20"
                            >
                              <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                            </svg>
                          ))}
                          <span className="text-sm text-gray-600">({item.reviewCount})</span>
                        </div>

                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleMoveToCart(item)}
                            className="amazon-btn-cart flex-1 text-sm py-2"
                          >
                            Add to Cart
                          </button>
                          <button
                            onClick={() => removeFromWishlist(item.id, activeListId)}
                            className="amazon-btn-secondary px-3 py-2 text-sm"
                            title="Remove from list"
                          >
                            Remove
                          </button>
                        </div>

                        {/* Move to other list */}
                        {lists.length > 1 && (
                          <div className="mt-2">
                            <select
                              onChange={(e) => {
                                if (e.target.value && e.target.value !== activeListId) {
                                  moveToList(item.id, activeListId, e.target.value);
                                }
                                e.target.value = '';
                              }}
                              className="w-full text-xs border border-gray-300 rounded px-2 py-1"
                              defaultValue=""
                            >
                              <option value="">Move to another list...</option>
                              {lists
                                .filter(list => list.id !== activeListId)
                                .map(list => (
                                  <option key={list.id} value={list.id}>
                                    {list.name}
                                  </option>
                                ))
                              }
                            </select>
                          </div>
                        )}

                        <div className="text-xs text-gray-500 mt-2">
                          Added {new Date(item.addedAt).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
