import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useOrderStore = create(
  persist(
    (set, get) => ({
      // State
      orders: [],
      currentOrder: null,
      isLoading: false,
      error: null,

      // Actions
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      // Create order from cart items
      createOrder: (cartItems, shippingAddress, paymentMethod) => {
        const orderId = `ORDER-${Date.now()}`;
        const orderDate = new Date().toISOString();
        
        const order = {
          id: orderId,
          date: orderDate,
          status: 'Processing',
          items: cartItems.map(item => ({
            ...item,
            orderedQuantity: item.quantity,
            orderedPrice: item.price
          })),
          subtotal: cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0),
          tax: cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 0.08, // 8% tax
          shipping: cartItems.some(item => !item.freeShipping) ? 9.99 : 0,
          total: 0, // Will be calculated below
          shippingAddress: shippingAddress || {
            name: 'John Doe',
            street: '123 Main St',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            country: 'USA'
          },
          paymentMethod: paymentMethod || {
            type: 'Credit Card',
            last4: '1234',
            brand: 'Visa'
          },
          estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
          trackingNumber: `TRK${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
          timeline: [
            {
              status: 'Order Placed',
              date: orderDate,
              description: 'Your order has been placed successfully'
            }
          ]
        };

        // Calculate total
        order.total = order.subtotal + order.tax + order.shipping;

        set(state => ({
          orders: [order, ...state.orders],
          currentOrder: order
        }));

        // Simulate order processing
        setTimeout(() => {
          get().updateOrderStatus(orderId, 'Confirmed', 'Your order has been confirmed and is being prepared');
        }, 2000);

        setTimeout(() => {
          get().updateOrderStatus(orderId, 'Shipped', 'Your order has been shipped');
        }, 5000);

        return order;
      },

      // Update order status
      updateOrderStatus: (orderId, status, description) => {
        set(state => ({
          orders: state.orders.map(order => {
            if (order.id === orderId) {
              const updatedOrder = {
                ...order,
                status,
                timeline: [
                  ...order.timeline,
                  {
                    status,
                    date: new Date().toISOString(),
                    description
                  }
                ]
              };

              // Add delivery date if delivered
              if (status === 'Delivered') {
                updatedOrder.deliveryDate = new Date().toISOString();
              }

              return updatedOrder;
            }
            return order;
          })
        }));
      },

      // Cancel order
      cancelOrder: (orderId, reason = 'Customer request') => {
        set(state => ({
          orders: state.orders.map(order => {
            if (order.id === orderId && ['Processing', 'Confirmed'].includes(order.status)) {
              return {
                ...order,
                status: 'Cancelled',
                cancellationReason: reason,
                timeline: [
                  ...order.timeline,
                  {
                    status: 'Cancelled',
                    date: new Date().toISOString(),
                    description: `Order cancelled: ${reason}`
                  }
                ]
              };
            }
            return order;
          })
        }));
      },

      // Return item
      returnItem: (orderId, itemId, reason = 'Not as described') => {
        set(state => ({
          orders: state.orders.map(order => {
            if (order.id === orderId) {
              return {
                ...order,
                items: order.items.map(item => {
                  if (item.id === itemId) {
                    return {
                      ...item,
                      returnStatus: 'Return Requested',
                      returnReason: reason,
                      returnDate: new Date().toISOString()
                    };
                  }
                  return item;
                }),
                timeline: [
                  ...order.timeline,
                  {
                    status: 'Return Requested',
                    date: new Date().toISOString(),
                    description: `Return requested for item: ${order.items.find(i => i.id === itemId)?.title}`
                  }
                ]
              };
            }
            return order;
          })
        }));
      },

      // Get order by ID
      getOrderById: (orderId) => {
        return get().orders.find(order => order.id === orderId);
      },

      // Get orders by status
      getOrdersByStatus: (status) => {
        return get().orders.filter(order => order.status === status);
      },

      // Get recent orders
      getRecentOrders: (limit = 5) => {
        return get().orders
          .sort((a, b) => new Date(b.date) - new Date(a.date))
          .slice(0, limit);
      },

      // Search orders
      searchOrders: (query) => {
        const searchTerm = query.toLowerCase();
        return get().orders.filter(order => 
          order.id.toLowerCase().includes(searchTerm) ||
          order.items.some(item => 
            item.title.toLowerCase().includes(searchTerm)
          )
        );
      },

      // Get order statistics
      getOrderStats: () => {
        const orders = get().orders;
        return {
          total: orders.length,
          processing: orders.filter(o => o.status === 'Processing').length,
          shipped: orders.filter(o => o.status === 'Shipped').length,
          delivered: orders.filter(o => o.status === 'Delivered').length,
          cancelled: orders.filter(o => o.status === 'Cancelled').length,
          totalSpent: orders
            .filter(o => o.status !== 'Cancelled')
            .reduce((sum, order) => sum + order.total, 0)
        };
      },

      // Reorder items
      reorderItems: (orderId) => {
        const order = get().getOrderById(orderId);
        if (order) {
          return order.items.map(item => ({
            id: item.id,
            title: item.title,
            price: item.price,
            image: item.image,
            quantity: item.orderedQuantity
          }));
        }
        return [];
      },

      // Track package
      trackPackage: (orderId) => {
        const order = get().getOrderById(orderId);
        if (order && order.trackingNumber) {
          // In a real app, this would call a tracking API
          return {
            trackingNumber: order.trackingNumber,
            status: order.status,
            estimatedDelivery: order.estimatedDelivery,
            timeline: order.timeline,
            currentLocation: 'Distribution Center - New York, NY'
          };
        }
        return null;
      },

      // Clear all orders (for testing)
      clearOrders: () => set({ orders: [], currentOrder: null })
    }),
    {
      name: 'order-storage',
      partialize: (state) => ({
        orders: state.orders
      })
    }
  )
);

export default useOrderStore;
