import { useState, useEffect } from 'react';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';
import ProductCard from '../components/Amazon/ProductCard';
import { mockProducts } from '../data/products';

export default function TodaysDeals() {
  const [deals, setDeals] = useState([]);
  const [sortBy, setSortBy] = useState('discount');
  const [filterBy, setFilterBy] = useState({
    category: 'all',
    discount: 'all'
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    
    // Simulate API delay
    setTimeout(() => {
      // Get products with discounts (originalPrice > price)
      let results = mockProducts.filter(product => 
        product.originalPrice && product.originalPrice > product.price
      );

      // Add discount percentage
      results = results.map(product => ({
        ...product,
        discountPercent: Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
      }));

      // Apply filters
      if (filterBy.category !== 'all') {
        results = results.filter(product =>
          product.category.toLowerCase().includes(filterBy.category.toLowerCase())
        );
      }

      if (filterBy.discount !== 'all') {
        const minDiscount = Number(filterBy.discount);
        results = results.filter(product => product.discountPercent >= minDiscount);
      }

      // Sort results
      switch (sortBy) {
        case 'discount':
          results.sort((a, b) => b.discountPercent - a.discountPercent);
          break;
        case 'price-low':
          results.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          results.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          results.sort((a, b) => b.rating - a.rating);
          break;
        default:
          break;
      }

      setDeals(results);
      setIsLoading(false);
    }, 500);
  }, [sortBy, filterBy]);

  const handleFilterChange = (filterType, value) => {
    setFilterBy(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const categories = ['all', 'Electronics', 'Books', 'Clothing & Shoes', 'Home & Kitchen', 'Sports & Outdoors', 'Toys & Games'];
  const discountRanges = [
    { label: 'All Discounts', value: 'all' },
    { label: '10% or more', value: '10' },
    { label: '25% or more', value: '25' },
    { label: '50% or more', value: '50' },
    { label: '70% or more', value: '70' }
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      {/* Hero Banner */}
      <div className="bg-gradient-to-r from-red-600 to-orange-600 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Today's Deals</h1>
          <p className="text-xl">Limited-time offers on your favorite products</p>
          <div className="mt-6 flex justify-center space-x-8 text-sm">
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              Limited Time
            </div>
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Best Prices
            </div>
            <div className="flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
              Free Shipping
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Filters and Sort */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <label htmlFor="category" className="text-sm font-medium text-gray-700">
                Category:
              </label>
              <select
                id="category"
                value={filterBy.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat === 'all' ? 'All Categories' : cat}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <label htmlFor="discount" className="text-sm font-medium text-gray-700">
                Discount:
              </label>
              <select
                id="discount"
                value={filterBy.discount}
                onChange={(e) => handleFilterChange('discount', e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                {discountRanges.map((range) => (
                  <option key={range.value} value={range.value}>
                    {range.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <label htmlFor="sort" className="text-sm font-medium text-gray-700">
              Sort by:
            </label>
            <select
              id="sort"
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="discount">Highest Discount</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="rating">Customer Rating</option>
            </select>
          </div>
        </div>

        {/* Results Header */}
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-900">
            {isLoading ? 'Loading deals...' : `${deals.length} deals found`}
          </h2>
        </div>

        {/* Deals Grid */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="loading-spinner w-8 h-8"></div>
          </div>
        ) : deals.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎯</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No deals found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your filters to find more deals.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {deals.map((product) => (
              <div key={product.id} className="relative">
                {/* Discount Badge */}
                <div className="absolute top-2 left-2 z-10 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                  {product.discountPercent}% OFF
                </div>
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        )}

        {/* Deal Categories */}
        <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6">Shop Deals by Category</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[
              { name: 'Electronics', icon: '📱', color: 'bg-blue-100' },
              { name: 'Fashion', icon: '👕', color: 'bg-purple-100' },
              { name: 'Home', icon: '🏠', color: 'bg-yellow-100' },
              { name: 'Books', icon: '📚', color: 'bg-green-100' },
              { name: 'Sports', icon: '⚽', color: 'bg-red-100' },
              { name: 'Toys', icon: '🧸', color: 'bg-pink-100' }
            ].map((category) => (
              <button
                key={category.name}
                onClick={() => handleFilterChange('category', category.name)}
                className={`${category.color} p-4 rounded-lg text-center hover:shadow-md transition-shadow cursor-pointer`}
              >
                <div className="text-3xl mb-2">{category.icon}</div>
                <h4 className="font-semibold text-gray-900 text-sm">{category.name}</h4>
              </button>
            ))}
          </div>
        </div>

        {/* Deal Tips */}
        <div className="mt-8 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">💡 Deal Shopping Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div className="flex items-start">
              <span className="mr-2">🕒</span>
              <div>
                <strong>Limited Time:</strong> These deals won't last long. Shop now to secure the best prices.
              </div>
            </div>
            <div className="flex items-start">
              <span className="mr-2">📦</span>
              <div>
                <strong>Prime Shipping:</strong> Get free two-day shipping on eligible items with Prime.
              </div>
            </div>
            <div className="flex items-start">
              <span className="mr-2">💳</span>
              <div>
                <strong>Payment Options:</strong> Use your Amazon credit card for additional rewards.
              </div>
            </div>
            <div className="flex items-start">
              <span className="mr-2">🔄</span>
              <div>
                <strong>Easy Returns:</strong> Not satisfied? Return items within 30 days for a full refund.
              </div>
            </div>
          </div>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
