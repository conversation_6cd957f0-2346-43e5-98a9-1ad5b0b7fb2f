import { Link } from 'react-router-dom';
import AmazonHeader from '../components/Amazon/Header';
import AmazonNavigation from '../components/Amazon/Navigation';
import AmazonFooter from '../components/Amazon/Footer';

export default function Prime() {
  const benefits = [
    {
      icon: '🚚',
      title: 'FREE Two-Day Shipping',
      description: 'Get free two-day shipping on millions of items with no minimum order size.'
    },
    {
      icon: '📺',
      title: 'Prime Video',
      description: 'Stream thousands of movies and TV shows, including award-winning Amazon Originals.'
    },
    {
      icon: '🎵',
      title: 'Prime Music',
      description: 'Listen to over 2 million songs and thousands of playlists and stations ad-free.'
    },
    {
      icon: '📚',
      title: 'Prime Reading',
      description: 'Read unlimited books, magazines, and comics on any device.'
    },
    {
      icon: '📸',
      title: 'Amazon Photos',
      description: 'Secure, unlimited photo storage in the cloud for Prime members.'
    },
    {
      icon: '🎮',
      title: 'Prime Gaming',
      description: 'Get free games, in-game content, and a free Twitch channel subscription every month.'
    },
    {
      icon: '🛒',
      title: 'Exclusive Deals',
      description: 'Get exclusive access to deals and discounts before anyone else.'
    },
    {
      icon: '⚡',
      title: 'Same-Day Delivery',
      description: 'Get same-day delivery on eligible items in select areas.'
    }
  ];

  const plans = [
    {
      name: 'Monthly',
      price: '$14.99',
      period: '/month',
      description: 'Perfect for trying Prime',
      features: ['All Prime benefits', 'Cancel anytime', 'No commitment']
    },
    {
      name: 'Annual',
      price: '$139',
      period: '/year',
      description: 'Best value - Save $40',
      features: ['All Prime benefits', 'Save over monthly', '30-day free trial'],
      popular: true
    },
    {
      name: 'Student',
      price: '$69',
      period: '/year',
      description: 'Exclusive student pricing',
      features: ['All Prime benefits', '6-month free trial', 'Student verification required']
    }
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <AmazonHeader />
      <AmazonNavigation />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="text-6xl mb-4">
              <span className="bg-blue-500 text-white px-4 py-2 rounded-lg font-bold">prime</span>
            </div>
            <h1 className="text-4xl font-bold mb-4">
              All the convenience you want. All the value you love.
            </h1>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join Prime and enjoy fast, free delivery on millions of items, plus exclusive perks and savings.
            </p>
            <div className="space-x-4">
              <button className="bg-orange-400 hover:bg-orange-500 text-black font-bold py-3 px-8 rounded-lg text-lg transition-colors">
                Start your 30-day free trial
              </button>
              <Link to="/" className="border-2 border-white text-white hover:bg-white hover:text-blue-800 font-bold py-3 px-8 rounded-lg text-lg transition-colors inline-block">
                Learn more
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Prime Benefits</h2>
          <p className="text-lg text-gray-600">Everything you need, delivered fast and free</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow">
              <div className="text-4xl mb-4">{benefit.icon}</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{benefit.title}</h3>
              <p className="text-gray-600 text-sm">{benefit.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Pricing Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
            <p className="text-lg text-gray-600">Start with a free trial, then choose the plan that works for you</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <div key={index} className={`rounded-lg border-2 p-8 relative ${
                plan.popular 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-200 bg-white'
              }`}>
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600">{plan.period}</span>
                  </div>
                  <p className="text-gray-600 mb-6">{plan.description}</p>
                  
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center justify-center">
                        <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                    plan.popular
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                  }`}>
                    {plan.name === 'Student' ? 'Verify Student Status' : 'Start Free Trial'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">What is Amazon Prime?</h3>
            <p className="text-gray-600">
              Amazon Prime is a membership program that gives you access to fast, free shipping and exclusive shopping and entertainment benefits.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">How much does Prime cost?</h3>
            <p className="text-gray-600">
              Prime costs $14.99/month or $139/year. Students can get Prime for $69/year with a valid .edu email address.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I cancel anytime?</h3>
            <p className="text-gray-600">
              Yes, you can cancel your Prime membership at any time. If you cancel during your free trial, you won't be charged.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">What's included with Prime?</h3>
            <p className="text-gray-600">
              Prime includes free shipping, Prime Video, Prime Music, Prime Reading, Amazon Photos, and exclusive deals and discounts.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-4">Ready to join Prime?</h2>
          <p className="text-xl mb-8">Start your 30-day free trial today and experience all the benefits Prime has to offer.</p>
          <button className="bg-orange-400 hover:bg-orange-500 text-black font-bold py-4 px-8 rounded-lg text-lg transition-colors">
            Start your free trial
          </button>
          <p className="text-sm mt-4 opacity-90">
            Cancel anytime during your free trial. After trial, membership auto-renews at $14.99/month.
          </p>
        </div>
      </div>

      <AmazonFooter />
    </div>
  );
}
